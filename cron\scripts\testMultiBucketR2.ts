// Test script for multi-bucket R2 system
import { 
  BucketType, 
  initializeAllBuckets, 
  getBucketName,
  uploadFile,
  downloadFile,
  deleteFile
} from "../lib/storage/r2Storage";
import { logger } from "../utils/logger";
import dotenv from "dotenv";

dotenv.config();

async function testMultiBucketSystem() {
  console.log("🧪 Testing Multi-Bucket R2 System...");
  
  try {
    // Test 1: Initialize all buckets
    console.log("\n📋 Step 1: Initializing all buckets...");
    const initResult = await initializeAllBuckets();
    
    if (initResult.success) {
      console.log("✅ All buckets initialized successfully!");
      
      // Show bucket names
      console.log("\n📦 Bucket Configuration:");
      for (const bucketType of Object.values(BucketType)) {
        const bucketName = getBucketName(bucketType);
        const status = initResult.results[bucketType] ? "✅" : "❌";
        console.log(`  ${status} ${bucketType}: ${bucketName}`);
      }
    } else {
      console.log("⚠️ Some buckets failed to initialize");
      console.log("Results:", initResult.results);
    }
    
    // Test 2: Test file upload to company bucket
    console.log("\n📤 Step 2: Testing file upload...");
    const testContent = Buffer.from("Test company logo content");
    const uploadResult = await uploadFile(
      testContent,
      "test-logo.png",
      "image/png",
      "companyLogos",
      "test-company-123"
    );
    
    if (uploadResult.success) {
      console.log(`✅ File uploaded successfully to ${uploadResult.bucketName}`);
      console.log(`   File key: ${uploadResult.fileKey}`);
      console.log(`   Public URL: ${uploadResult.publicUrl}`);
      
      // Test 3: Download the file
      console.log("\n📥 Step 3: Testing file download...");
      const downloadResult = await downloadFile(uploadResult.fileKey!, BucketType.COMPANY);
      
      if (downloadResult.success) {
        console.log("✅ File downloaded successfully");
        console.log(`   Size: ${downloadResult.buffer?.length} bytes`);
        
        // Test 4: Delete the file
        console.log("\n🗑️ Step 4: Testing file deletion...");
        const deleteResult = await deleteFile(uploadResult.fileKey!, BucketType.COMPANY);
        
        if (deleteResult.success) {
          console.log("✅ File deleted successfully");
        } else {
          console.log("❌ File deletion failed:", deleteResult.error);
        }
      } else {
        console.log("❌ File download failed:", downloadResult.error);
      }
    } else {
      console.log("❌ File upload failed:", uploadResult.error);
    }
    
    console.log("\n🎉 Multi-bucket test completed!");
    return true;
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testMultiBucketSystem()
    .then((success) => {
      if (success) {
        console.log("✅ All tests passed!");
        process.exit(0);
      } else {
        console.log("❌ Tests failed!");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("💥 Unexpected error:", error);
      process.exit(1);
    });
}

export { testMultiBucketSystem };
