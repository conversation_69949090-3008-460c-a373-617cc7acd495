// Test existing R2 buckets and upload capability
import { S3Client, ListBucketsCommand } from "@aws-sdk/client-s3";
import { uploadFile, BucketType, getBucketName } from "../lib/storage/r2Storage";
import dotenv from "dotenv";

dotenv.config();

async function testExistingBuckets() {
  console.log("🧪 Testing existing R2 buckets...");
  
  try {
    // First, update credentials with your actual R2 token
    console.log("\n⚠️  IMPORTANT: Make sure you've updated .env with your actual R2 credentials!");
    console.log("   Current credentials are test/placeholder values");
    
    const r2Client = new S3Client({
      endpoint: process.env.R2_ENDPOINT || "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
      region: "auto",
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
      },
    });

    // Test 1: List existing buckets
    console.log("\n📋 Step 1: Listing existing buckets...");
    const listResponse = await r2Client.send(new ListBucketsCommand({}));
    
    if (listResponse.Buckets && listResponse.Buckets.length > 0) {
      console.log(`✅ Found ${listResponse.Buckets.length} existing buckets:`);
      listResponse.Buckets.forEach(bucket => {
        console.log(`   • ${bucket.Name} (created: ${bucket.CreationDate})`);
      });
    } else {
      console.log("❌ No buckets found");
      return false;
    }

    // Test 2: Check which of our expected buckets exist
    console.log("\n🔍 Step 2: Checking expected bucket names...");
    const expectedBuckets = Object.values(BucketType).map(type => getBucketName(type));
    const existingBucketNames = listResponse.Buckets?.map(b => b.Name) || [];
    
    expectedBuckets.forEach(expectedName => {
      const exists = existingBucketNames.includes(expectedName);
      const status = exists ? "✅" : "❌";
      console.log(`   ${status} ${expectedName}`);
    });

    // Test 3: Try uploading to the company bucket (most likely to exist)
    const companyBucketName = getBucketName(BucketType.COMPANY);
    if (existingBucketNames.includes(companyBucketName)) {
      console.log(`\n📤 Step 3: Testing upload to ${companyBucketName}...`);
      
      const testContent = Buffer.from("Test company logo - " + new Date().toISOString());
      const uploadResult = await uploadFile(
        testContent,
        "test-logo.png",
        "image/png",
        "companyLogos",
        "test-company-123"
      );
      
      if (uploadResult.success) {
        console.log("✅ Upload successful!");
        console.log(`   File key: ${uploadResult.fileKey}`);
        console.log(`   Public URL: ${uploadResult.publicUrl}`);
        console.log(`   Bucket: ${uploadResult.bucketName}`);
        return true;
      } else {
        console.log("❌ Upload failed:", uploadResult.error);
        return false;
      }
    } else {
      console.log(`\n❌ Company bucket '${companyBucketName}' not found`);
      console.log("\n💡 Please create these buckets in Cloudflare dashboard:");
      expectedBuckets.forEach(name => {
        console.log(`   • ${name}`);
      });
      return false;
    }

  } catch (error: any) {
    console.error("❌ Test failed:", error.message);
    
    if (error.message.includes("Access Denied") || error.message.includes("403")) {
      console.log("\n💡 This looks like a credentials issue. Please:");
      console.log("   1. Get your actual R2 Access Key ID and Secret from Cloudflare dashboard");
      console.log("   2. Update the .env file with the real credentials");
      console.log("   3. Make sure the token has 'Object Read & Write' permissions");
    }
    
    return false;
  }
}

testExistingBuckets()
  .then((success) => {
    if (success) {
      console.log("\n🎉 R2 buckets are working! Ready to process company logos!");
    } else {
      console.log("\n❌ R2 setup needs attention before proceeding");
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
  });
