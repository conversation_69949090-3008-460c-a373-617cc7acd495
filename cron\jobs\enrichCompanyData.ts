// cron/jobs/enrichCompanyData.ts
// Cron job to enrich company data with domains, websites, and logos

import { logger } from "../utils/logger";
import { prisma } from "../lib/prismaClient";
import {
  ensureWebsitesFromDomains,
  ensureCompanyLogosFromWebsites,
} from "../lib/enrichCompanyData";
import dotenv from "dotenv";
import path from "path";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), ".env") });

/**
 * Main function to enrich company data
 * This job runs in sequence:
 * 1. Ensures websites are set for companies with domains
 * 2. Ensures logos are set for companies with websites
 */
async function enrichCompanyData() {
  const startTime = Date.now();
  logger.info("🚀 Starting company data enrichment job");

  try {
    // Step 1: Ensure websites are set from domains
    logger.info("📝 Step 1: Ensuring websites from domains...");
    const websitesAdded = await ensureWebsitesFromDomains(prisma);
    logger.info(`✅ Step 1 complete: ${websitesAdded} websites added`);

    // Step 2: Ensure logos are set from websites
    logger.info("🎨 Step 2: Ensuring logos from websites...");
    const logosAdded = await ensureCompanyLogosFromWebsites(prisma);
    logger.info(`✅ Step 2 complete: ${logosAdded} logos added`);

    const duration = Date.now() - startTime;
    logger.info(
      `🎉 Company data enrichment completed successfully in ${duration}ms`
    );
    logger.info(
      `📊 Summary: ${websitesAdded} websites added, ${logosAdded} logos added`
    );

    return {
      success: true,
      websitesAdded,
      logosAdded,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(
      `❌ Company data enrichment failed after ${duration}ms:`,
      error
    );

    return {
      success: false,
      error: error.message,
      duration,
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Export for use in other modules
export { enrichCompanyData };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  enrichCompanyData()
    .then((result) => {
      if (result.success) {
        logger.info("✅ Company data enrichment job completed successfully");
        process.exit(0);
      } else {
        logger.error("❌ Company data enrichment job failed");
        process.exit(1);
      }
    })
    .catch((error) => {
      logger.error(
        "💥 Unexpected error in company data enrichment job:",
        error
      );
      process.exit(1);
    });
}
