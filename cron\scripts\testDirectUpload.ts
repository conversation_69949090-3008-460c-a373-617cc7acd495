// Test direct upload to R2 without bucket checks
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import dotenv from "dotenv";

dotenv.config();

async function testDirectUpload() {
  console.log("🧪 Testing direct upload to R2...");
  
  try {
    const r2Client = new S3Client({
      endpoint: process.env.R2_ENDPOINT || "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
      region: "auto",
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
      },
    });

    // Test upload to company logos bucket
    const bucketName = "hirli-company-logos";
    const fileKey = `test/test-logo-${Date.now()}.png`;
    const testContent = Buffer.from("Test company logo content - " + new Date().toISOString());

    console.log(`📤 Uploading test file to ${bucketName}/${fileKey}...`);

    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
      Body: testContent,
      ContentType: "image/png",
      Metadata: {
        originalName: "test-logo.png",
        uploadedAt: new Date().toISOString(),
        fileType: "companyLogos",
        bucketType: "company",
        companyId: "test-company-123",
      },
    });

    await r2Client.send(uploadCommand);

    const accountId = process.env.R2_ACCOUNT_ID;
    const publicUrl = `https://${bucketName}.${accountId}.r2.dev/${fileKey}`;

    console.log("✅ Upload successful!");
    console.log(`   Bucket: ${bucketName}`);
    console.log(`   File key: ${fileKey}`);
    console.log(`   Public URL: ${publicUrl}`);
    console.log(`   Size: ${testContent.length} bytes`);

    return true;

  } catch (error: any) {
    console.error("❌ Upload failed:", error.message);
    
    if (error.$metadata) {
      console.log(`   HTTP Status: ${error.$metadata.httpStatusCode}`);
    }
    
    if (error.message.includes("Access Denied") || error.message.includes("403")) {
      console.log("\n💡 This might be a permissions issue:");
      console.log("   1. Make sure your R2 token has 'Object Read & Write' permissions");
      console.log("   2. Verify the bucket name is correct");
      console.log("   3. Check if the bucket exists in Cloudflare dashboard");
    }
    
    return false;
  }
}

testDirectUpload()
  .then((success) => {
    if (success) {
      console.log("\n🎉 Direct upload test successful! R2 is working!");
      console.log("💡 The bucket check might be failing due to permissions, but uploads work.");
      console.log("   We can modify the code to skip bucket checks and upload directly.");
    } else {
      console.log("\n❌ Direct upload test failed");
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
  });
