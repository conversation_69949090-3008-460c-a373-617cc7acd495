// cron/scripts/testR2Connection.ts
// Test script to verify R2 connection and create bucket if needed

import {
  S3Client,
  ListBucketsCommand,
  CreateBucketCommand,
  HeadBucketCommand,
} from "@aws-sdk/client-s3";
import { logger } from "../utils/logger";

// R2 Configuration
const R2_CONFIG = {
  endpoint: "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
  region: "auto",
  credentials: {
    accessKeyId: "c3a71f217fdf3a056efaefab3a17afc5",
    secretAccessKey:
      "****************************************************************",
  },
};

const BUCKET_NAME = "auto-apply-assets";

async function testR2Connection() {
  logger.info("🧪 Testing Cloudflare R2 connection...");

  try {
    // Initialize R2 client
    const r2Client = new S3Client(R2_CONFIG);

    // Test 1: List buckets to verify connection
    logger.info("📋 Step 1: Testing connection by listing buckets...");
    const listBucketsResponse = await r2Client.send(new ListBucketsCommand({}));

    logger.info(
      `✅ Connection successful! Found ${listBucketsResponse.Buckets?.length || 0} buckets`
    );

    if (listBucketsResponse.Buckets && listBucketsResponse.Buckets.length > 0) {
      logger.info("📦 Existing buckets:");
      listBucketsResponse.Buckets.forEach((bucket) => {
        logger.info(`  • ${bucket.Name} (created: ${bucket.CreationDate})`);
      });
    }

    // Test 2: Check if our bucket exists
    logger.info(`🔍 Step 2: Checking if bucket '${BUCKET_NAME}' exists...`);

    let bucketExists = false;
    try {
      await r2Client.send(new HeadBucketCommand({ Bucket: BUCKET_NAME }));
      bucketExists = true;
      logger.info(`✅ Bucket '${BUCKET_NAME}' exists and is accessible`);
    } catch (error: any) {
      if (
        error.name === "NotFound" ||
        error.$metadata?.httpStatusCode === 404
      ) {
        logger.info(`📦 Bucket '${BUCKET_NAME}' does not exist`);
      } else {
        logger.warn(`⚠️ Error checking bucket: ${error.message}`);
      }
    }

    // Test 3: Create bucket if it doesn't exist
    if (!bucketExists) {
      logger.info(`🏗️ Step 3: Creating bucket '${BUCKET_NAME}'...`);

      try {
        await r2Client.send(new CreateBucketCommand({ Bucket: BUCKET_NAME }));
        logger.info(`✅ Bucket '${BUCKET_NAME}' created successfully!`);
      } catch (error: any) {
        if (
          error.name === "BucketAlreadyExists" ||
          error.name === "BucketAlreadyOwnedByYou"
        ) {
          logger.info(`✅ Bucket '${BUCKET_NAME}' already exists`);
        } else {
          logger.error(`❌ Failed to create bucket: ${error.message}`);
          throw error;
        }
      }
    }

    // Test 4: Final verification
    logger.info("🔄 Step 4: Final verification...");
    await r2Client.send(new HeadBucketCommand({ Bucket: BUCKET_NAME }));
    logger.info(`✅ Bucket '${BUCKET_NAME}' is ready for use!`);

    // Summary
    logger.info("🎉 R2 Setup Complete!");
    logger.info("📊 Configuration Summary:");
    logger.info(`  • Account ID: 7efc1bf67e7d23f5683e06d0227c883f`);
    logger.info(`  • Endpoint: ${R2_CONFIG.endpoint}`);
    logger.info(`  • Bucket: ${BUCKET_NAME}`);
    logger.info(`  • Access Key: ${R2_CONFIG.credentials.accessKeyId}`);
    logger.info("🚀 Ready to start uploading company logos!");

    return true;
  } catch (error) {
    logger.error("❌ R2 connection test failed:", error);

    // Provide helpful error messages
    if (error instanceof Error) {
      if (error.message.includes("InvalidAccessKeyId")) {
        logger.error(
          "🔑 Invalid Access Key ID. Please check your R2 credentials."
        );
      } else if (error.message.includes("SignatureDoesNotMatch")) {
        logger.error(
          "🔐 Invalid Secret Access Key. Please check your R2 credentials."
        );
      } else if (error.message.includes("ENOTFOUND")) {
        logger.error(
          "🌐 Network error. Please check your internet connection."
        );
      } else {
        logger.error(`💥 Unexpected error: ${error.message}`);
      }
    }

    return false;
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testR2Connection()
    .then((success) => {
      if (success) {
        logger.info("✅ All tests passed! R2 is ready to use.");
        process.exit(0);
      } else {
        logger.error("❌ Tests failed. Please check your configuration.");
        process.exit(1);
      }
    })
    .catch((error) => {
      logger.error("💥 Unexpected error:", error);
      process.exit(1);
    });
}

export { testR2Connection };
