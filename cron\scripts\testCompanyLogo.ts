// Test script to verify company logo upload to R2
import { prisma } from "../lib/prismaClient";
import { downloadAndProcessImage } from "../lib/storage/imageProcessor";
import { initializeAllBuckets, BucketType } from "../lib/storage/r2Storage";
import { logger } from "../utils/logger";
import dotenv from "dotenv";

dotenv.config();

async function testCompanyLogoUpload() {
  console.log("🧪 Testing Company Logo Upload to R2...");
  
  try {
    // Step 1: Initialize buckets
    console.log("\n📋 Step 1: Initializing R2 buckets...");
    const initResult = await initializeAllBuckets();
    
    if (!initResult.success) {
      console.log("❌ Failed to initialize buckets:", initResult.results);
      return false;
    }
    console.log("✅ All buckets initialized successfully!");

    // Step 2: Find a company without a logo
    console.log("\n🔍 Step 2: Finding a company without a logo...");
    const company = await prisma.company.findFirst({
      where: {
        logoUrl: null,
        domain: { not: null },
      },
      select: {
        id: true,
        name: true,
        domain: true,
        website: true,
      }
    });

    if (!company) {
      console.log("ℹ️ No companies found without logos. Let's find one with a logo to test replacement...");
      
      const companyWithLogo = await prisma.company.findFirst({
        where: {
          logoUrl: { not: null },
          domain: { not: null },
        },
        select: {
          id: true,
          name: true,
          domain: true,
          website: true,
          logoUrl: true,
        }
      });

      if (!companyWithLogo) {
        console.log("❌ No companies found with domains. Cannot test.");
        return false;
      }

      console.log(`📍 Found company: ${companyWithLogo.name}`);
      console.log(`   Domain: ${companyWithLogo.domain}`);
      console.log(`   Current logo: ${companyWithLogo.logoUrl}`);
      
      // Test with Clearbit logo
      const testLogoUrl = `https://logo.clearbit.com/${companyWithLogo.domain}`;
      console.log(`   Test logo URL: ${testLogoUrl}`);

      // Step 3: Download and process the logo
      console.log("\n📥 Step 3: Downloading and processing logo...");
      const fileName = `${companyWithLogo.name.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase()}-test-logo`;
      
      const processResult = await downloadAndProcessImage(
        testLogoUrl,
        fileName,
        companyWithLogo.id
      );

      if (processResult.success) {
        console.log("✅ Logo processed successfully!");
        console.log(`   Original: ${processResult.original?.publicUrl}`);
        console.log(`   Optimized: ${processResult.optimized?.publicUrl}`);
        console.log(`   Thumbnail: ${processResult.thumbnail?.publicUrl}`);
        console.log(`   Bucket: ${processResult.optimized?.bucketName}`);
        
        // Step 4: Update company with new logo (optional)
        console.log("\n💾 Step 4: Updating company with new logo...");
        await prisma.company.update({
          where: { id: companyWithLogo.id },
          data: {
            logoUrl: processResult.optimized?.publicUrl,
            logoVariants: {
              original: processResult.original?.publicUrl,
              optimized: processResult.optimized?.publicUrl,
              thumbnail: processResult.thumbnail?.publicUrl,
            },
          },
        });
        console.log("✅ Company updated with new logo!");
        
        return true;
      } else {
        console.log("❌ Logo processing failed:", processResult.error);
        return false;
      }
    } else {
      console.log(`📍 Found company without logo: ${company.name}`);
      console.log(`   Domain: ${company.domain}`);
      console.log(`   Website: ${company.website}`);

      // Try Clearbit logo first
      const clearbitLogoUrl = `https://logo.clearbit.com/${company.domain}`;
      console.log(`   Trying Clearbit logo: ${clearbitLogoUrl}`);

      // Step 3: Download and process the logo
      console.log("\n📥 Step 3: Downloading and processing logo...");
      const fileName = `${company.name.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase()}-logo`;
      
      const processResult = await downloadAndProcessImage(
        clearbitLogoUrl,
        fileName,
        company.id
      );

      if (processResult.success) {
        console.log("✅ Logo processed successfully!");
        console.log(`   Original: ${processResult.original?.publicUrl}`);
        console.log(`   Optimized: ${processResult.optimized?.publicUrl}`);
        console.log(`   Thumbnail: ${processResult.thumbnail?.publicUrl}`);
        console.log(`   Bucket: ${processResult.optimized?.bucketName}`);
        
        // Step 4: Update company with new logo
        console.log("\n💾 Step 4: Updating company with new logo...");
        await prisma.company.update({
          where: { id: company.id },
          data: {
            logoUrl: processResult.optimized?.publicUrl,
            logoVariants: {
              original: processResult.original?.publicUrl,
              optimized: processResult.optimized?.publicUrl,
              thumbnail: processResult.thumbnail?.publicUrl,
            },
          },
        });
        console.log("✅ Company updated with new logo!");
        
        return true;
      } else {
        console.log("❌ Logo processing failed:", processResult.error);
        return false;
      }
    }

  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testCompanyLogoUpload()
    .then((success) => {
      if (success) {
        console.log("\n🎉 Company logo test completed successfully!");
        process.exit(0);
      } else {
        console.log("\n❌ Company logo test failed!");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("💥 Unexpected error:", error);
      process.exit(1);
    });
}

export { testCompanyLogoUpload };
