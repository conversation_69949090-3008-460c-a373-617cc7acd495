// Check environment variables
import dotenv from "dotenv";
import path from "path";

// Load .env file from cron directory
dotenv.config({ path: path.join(process.cwd(), ".env") });
console.log(`Loading .env from: ${path.join(process.cwd(), ".env")}`);

console.log("🔍 Checking environment variables...");
console.log(
  `R2_ACCOUNT_ID: ${process.env.R2_ACCOUNT_ID ? "Set (" + process.env.R2_ACCOUNT_ID.length + " chars)" : "NOT SET"}`
);
console.log(`R2_ENDPOINT: ${process.env.R2_ENDPOINT ? "Set" : "NOT SET"}`);
console.log(
  `R2_ACCESS_KEY_ID: ${process.env.R2_ACCESS_KEY_ID ? "Set (" + process.env.R2_ACCESS_KEY_ID.length + " chars)" : "NOT SET"}`
);
console.log(
  `R2_SECRET_ACCESS_KEY: ${process.env.R2_SECRET_ACCESS_KEY ? "Set (" + process.env.R2_SECRET_ACCESS_KEY.length + " chars)" : "NOT SET"}`
);

if (process.env.R2_ACCESS_KEY_ID) {
  console.log(`Access Key ID: ${process.env.R2_ACCESS_KEY_ID}`);
}
if (process.env.R2_SECRET_ACCESS_KEY) {
  console.log(
    `Secret Access Key: ${process.env.R2_SECRET_ACCESS_KEY.substring(0, 10)}...`
  );
}
