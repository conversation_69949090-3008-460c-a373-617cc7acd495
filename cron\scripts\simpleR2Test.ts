// Simple R2 test without complex logging
import { S3Client, ListBucketsCommand } from "@aws-sdk/client-s3";
import dotenv from "dotenv";

dotenv.config();

const r2Client = new S3Client({
  endpoint: process.env.R2_ENDPOINT || "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
  region: "auto",
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
  },
});

async function testConnection() {
  try {
    console.log("🧪 Testing R2 connection...");
    const response = await r2Client.send(new ListBucketsCommand({}));
    console.log("✅ Connection successful!");
    console.log(`Found ${response.Buckets?.length || 0} buckets:`);
    response.Buckets?.forEach(bucket => {
      console.log(`  • ${bucket.Name}`);
    });
    return true;
  } catch (error) {
    console.error("❌ Connection failed:", error);
    return false;
  }
}

testConnection().then(success => {
  process.exit(success ? 0 : 1);
});
