// Check company logo status
import { prisma } from "../lib/prismaClient";

async function checkCompanyLogos() {
  try {
    console.log("🔍 Checking company logo status...");

    // Count companies with and without logos
    const totalCompanies = await prisma.company.count();
    const companiesWithLogos = await prisma.company.count({
      where: { logoUrl: { not: null } },
    });
    const companiesWithoutLogos = await prisma.company.count({
      where: { logoUrl: null },
    });
    const companiesWithDomains = await prisma.company.count({
      where: { domain: { not: null } },
    });
    const companiesWithWebsites = await prisma.company.count({
      where: { website: { not: null } },
    });

    console.log("\n📊 Company Statistics:");
    console.log(`   Total companies: ${totalCompanies}`);
    console.log(`   Companies with domains: ${companiesWithDomains}`);
    console.log(`   Companies with websites: ${companiesWithWebsites}`);
    console.log(`   Companies with logos: ${companiesWithLogos}`);
    console.log(`   Companies without logos: ${companiesWithoutLogos}`);

    // Show some companies without logos that have domains
    const companiesNeedingLogos = await prisma.company.findMany({
      where: {
        logoUrl: null,
        domain: { not: null },
      },
      select: {
        id: true,
        name: true,
        domain: true,
        website: true,
      },
      take: 5,
    });

    if (companiesNeedingLogos.length > 0) {
      console.log("\n🎯 Companies that need logos (first 5):");
      companiesNeedingLogos.forEach((company, index) => {
        console.log(`   ${index + 1}. ${company.name}`);
        console.log(`      Domain: ${company.domain}`);
        console.log(`      Website: ${company.website || "None"}`);
      });
    } else {
      console.log("\n✅ All companies with domains already have logos!");
    }

    // Show some recent companies with logos
    const recentLogos = await prisma.company.findMany({
      where: {
        logoUrl: { not: null },
      },
      select: {
        name: true,
        logoUrl: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 3,
    });

    if (recentLogos.length > 0) {
      console.log("\n🖼️ Recent companies with logos:");
      recentLogos.forEach((company, index) => {
        console.log(`   ${index + 1}. ${company.name}`);
        console.log(`      Logo URL: ${company.logoUrl}`);
        if (company.logoVariants) {
          console.log(`      Has variants: Yes`);
        }
      });
    }
  } catch (error) {
    console.error("❌ Error checking company logos:", error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCompanyLogos();
