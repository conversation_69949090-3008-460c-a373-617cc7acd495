// Test logo processing directly
import { ensureCompanyLogosFromWebsites } from "../lib/enrichCompanyData";
import { prisma } from "../lib/prismaClient";
import dotenv from "dotenv";
import path from "path";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env') });

async function testLogoProcessing() {
  console.log("🧪 Testing logo processing directly...");
  
  try {
    // Check how many companies need logos
    const companiesNeedingLogos = await prisma.company.count({
      where: {
        website: { not: null },
        logoUrl: null,
      }
    });
    
    console.log(`📊 Found ${companiesNeedingLogos} companies that need logos`);
    
    if (companiesNeedingLogos === 0) {
      console.log("✅ All companies with websites already have logos!");
      return true;
    }
    
    // Show a few companies that need logos
    const sampleCompanies = await prisma.company.findMany({
      where: {
        website: { not: null },
        logoUrl: null,
      },
      select: {
        name: true,
        website: true,
        domain: true,
      },
      take: 5
    });
    
    console.log("\n🎯 Sample companies that need logos:");
    sampleCompanies.forEach((company, index) => {
      console.log(`   ${index + 1}. ${company.name}`);
      console.log(`      Website: ${company.website}`);
      console.log(`      Domain: ${company.domain}`);
    });
    
    console.log("\n🚀 Running logo processing...");
    const logosAdded = await ensureCompanyLogosFromWebsites(prisma);
    
    console.log(`✅ Logo processing completed! Added ${logosAdded} logos`);
    
    return true;
    
  } catch (error) {
    console.error("❌ Logo processing failed:", error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

testLogoProcessing()
  .then((success) => {
    if (success) {
      console.log("\n🎉 Logo processing test completed!");
    } else {
      console.log("\n❌ Logo processing test failed!");
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
  });
