// Test company logo upload to R2
import { uploadFile } from "../lib/storage/r2Storage";
import { prisma } from "../lib/prismaClient";
import dotenv from "dotenv";
import path from "path";

// Load .env file from cron directory
dotenv.config({ path: path.join(process.cwd(), ".env") });

async function testCompanyLogoUpload() {
  console.log("🧪 Testing company logo upload to R2...");

  try {
    // Find a company that needs a logo
    const company = await prisma.company.findFirst({
      where: {
        logoUrl: null,
        domain: { not: null },
      },
      select: {
        id: true,
        name: true,
        domain: true,
      },
    });

    if (!company) {
      console.log("❌ No companies found that need logos");
      return false;
    }

    console.log(`📍 Testing with company: ${company.name}`);
    console.log(`   Domain: ${company.domain}`);

    // Create a test logo (simple PNG data)
    const testLogoContent = Buffer.from(
      "Test logo content for " + company.name
    );

    console.log("\n📤 Uploading test logo to R2...");

    const uploadResult = await uploadFile(
      testLogoContent,
      `${company.name.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase()}-logo.png`,
      "image/png",
      "companyLogos",
      company.id
    );

    if (uploadResult.success) {
      console.log("✅ Upload successful!");
      console.log(`   Bucket: ${uploadResult.bucketName}`);
      console.log(`   File key: ${uploadResult.fileKey}`);
      console.log(`   Public URL: ${uploadResult.publicUrl}`);
      console.log(`   Size: ${uploadResult.fileSize} bytes`);

      // Update the company with the logo URL
      console.log("\n💾 Updating company in database...");
      await prisma.company.update({
        where: { id: company.id },
        data: {
          logoUrl: uploadResult.publicUrl,
        },
      });

      console.log("✅ Company updated with logo URL!");

      // Verify the update
      const updatedCompany = await prisma.company.findUnique({
        where: { id: company.id },
        select: { name: true, logoUrl: true },
      });

      console.log(
        `✅ Verification: ${updatedCompany?.name} now has logo: ${updatedCompany?.logoUrl}`
      );

      return true;
    } else {
      console.log("❌ Upload failed:", uploadResult.error);
      return false;
    }
  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

testCompanyLogoUpload()
  .then((success) => {
    if (success) {
      console.log("\n🎉 Company logo upload test successful!");
      console.log("💡 The R2 multi-bucket system is working!");
      console.log("   You can now run: npm run enrich-company-data");
    } else {
      console.log("\n❌ Company logo upload test failed");
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
  });
